# vectorstore
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS

# chain
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain.schema.runnable import RunnablePassthrough, RunnableLambda

import os
from dotenv import load_dotenv
load_dotenv()

vectorstore_path = "./data/dji_store_vectorstore"

embeddings = OpenAIEmbeddings(model="text-embedding-3-large")

if os.path.exists(vectorstore_path):
    vectorstore = FAISS.load_local(
        vectorstore_path,
        embeddings,
        allow_dangerous_deserialization=True,
    )
    print("Vectorstore loaded successfully.\n\n")
else:
    print(f"[WARN] Vectorstore not found at {vectorstore_path}")
    exit(1)

# retriever = vectorstore.as_retriever(search_type="similarity", search_kwargs={"k": 3})
retriever = vectorstore.as_retriever(search_type="mmr", search_kwargs={"k": 6, "fetch_k": 20, "lambda_mult": 0.3})
# print(retriever.invoke("เปรียบเทียบ DJI Mavic 3 Pro กับ DJI Avata 2"))



# prompt template
SYSTEM_PROMPT = ("""
คุณคือผู้ช่วยตอบคำถามสำหรับร้านค้า  

- หากคำถามเป็นเพียงการทักทาย เช่น "สวัสดี", "hello", "hi"  
  ให้ตอบกลับด้วยคำทักทายที่สุภาพและเชิญชวนให้ถามข้อมูลสินค้า/บริการ เช่น:  
  "สวัสดีค่ะ ยินดีต้อนรับ หากสนใจสอบถามข้อมูลสินค้าและบริการ สามารถถามได้เลยนะคะ"  

- หากคำถามไม่เกี่ยวข้องกับสินค้า บริการ หรือร้านค้า ให้ตอบว่า:  
  "คำถามนี้ไม่เกี่ยวข้องกับร้านค้า หากมีคำถามเกี่ยวกับสินค้าและบริการ สามารถสอบถามเพิ่มเติมได้เลยค่ะ"  

- หากไม่พบคำตอบจากข้อมูลในเอกสาร ให้ตอบกลับว่า:  
  "กรุณาติดต่อเจ้าหน้าที่เกี่ยวกับ [ประเด็นคำถาม] [ช่องทางการติดต่อ]"  
  (แนะนำช่องทางการติดต่อเฉพาะกรณีนี้เท่านั้น)  e

- หากคำถามเกี่ยวข้องกับการเปรียบเทียบสินค้า ให้สร้างตารางสั้นๆ เพื่อเปรียบเทียบข้อดีและข้อเสียของสินค้าแต่ละตัว  
  (ไม่ต้องแนะนำช่องทางการติดต่อ)

- หากพบสินค้าให้แนะนำสินค้า  
  (ไม่ต้องแนะนำช่องทางการติดต่อ)  
  
"""
)

USER_PROMPT = ("""
คำถาม: {question}  
บริบท: {context}
"""
)

prompt = ChatPromptTemplate.from_messages([
    ("system", SYSTEM_PROMPT),
    ("user", USER_PROMPT),
])

# llm model
# llm = ChatOpenAI(model="gpt-5-mini", temperature=0)
from langchain_ollama import OllamaLLM
llm = OllamaLLM(model="qwen3:8b")

# chain
# chain = retriever | prompt | llm | StrOutputParser()
chain = (
    {"context": retriever, "question": RunnablePassthrough()}
    | prompt
    | llm
    | StrOutputParser()
)


result = chain.invoke("สวัสดีครับ")
print(result)
print('\n\n---------------------------------------------------------------------------------------\n')
result = chain.invoke("มี DJI Mavic 3 Pro ไหม")
print(result)
print('\n\n---------------------------------------------------------------------------------------\n')
result = chain.invoke("เปรียบเทียบ DJI Mavic 3 Pro กับ DJI Avata 2")
print(result)
print('\n\n---------------------------------------------------------------------------------------\n')
result = chain.invoke("มี DJI Avata 999X ไหม")
print(result)


