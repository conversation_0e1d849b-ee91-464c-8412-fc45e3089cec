from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_community.retrievers import BM25Retriever
from dotenv import load_dotenv
load_dotenv()

from doc_loader import load_docs

# ---------- 1) Load documents ----------
file = "./data/dji_store.xlsx"
docs = load_docs([file])

# ---------- 2) Split ----------
splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=140)
chunks = splitter.split_documents(docs)
if len(chunks) == 0:
    print(f"[ERROR] No chunks from {file}")
    exit(1)

# ---------- 3) Create vector store ----------
embeddings = OpenAIEmbeddings(model="text-embedding-3-large")

# ---------- 4) transform documents to vectorstore ----------
vectorstore = FAISS.from_documents(chunks, embeddings)

# ---------- 5) Save vectorstore ----------
vectorstore.save_local(f"./data/{file.split('/')[-1].split('.')[0]}_vectorstore")