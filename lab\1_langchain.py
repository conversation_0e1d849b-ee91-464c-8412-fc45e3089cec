from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser, CommaSeparatedListOutputParser

import requests
from langchain.agents import tool

from dotenv import load_dotenv
load_dotenv()

# llm model
# temp 0.3 แม่นยำสูง ตรงประเด็น, temp 0.7 หลากหลายแต่ยังคงแม่นยำ, [0.8-2 หลากหลาย คาดเดาไม่ได้] 1-1.5 เหมาะสำหรับคิดสร้างสรรค์ หากเกิน 1.5 อาจจะได้คำตอบไม่ตรงประเด็น
llm = ChatOpenAI(model="gpt-5-mini", temperature=0.3)

# llm test
response = llm.invoke("สวัสดีครับ")
print(response.content)
print('------------------------')

response = llm.invoke("นายกรัฐมนตรียคนล่าสุดของประเทศไทย ชื่ออะไร")
print(response.content)
print('------------------------')


# llm with tools search
tool_serach = {"type": "web_search_preview"}
llm_search = llm.bind_tools([tool_serach])

response = llm_search.invoke("นายกรัฐมนตรียคนล่าสุดของประเทศไทย ชื่ออะไร")
print(response.content[0]['text'])
print('------------------------')


# output parser
parser = StrOutputParser()
get_weather_chain = llm_search | parser

response = get_weather_chain.invoke("อากาศที่จังหวัดนราธิวาสเป็นยังไง")
print(response)
print('------------------------')



# prompt template ex1
prompt = ChatPromptTemplate.from_template("ยกตัวอย่างการใช้งาน {question}  1 ตัวอย่างด้วย {language}")
# message type template ex2
# prompt = ChatPromptTemplate.from_messages([
#     ("system", "คุณเป็นผู้ช่วยที่เชี่ยวชาญในการเขียนโปรแกรมด้วย {language}"),
#     ("user", "ช่วยอธิบายเกี่ยวกับการทำงานของ {question} ใน 1 ตัวอย่าง"),
# ])

# chain
chain = prompt | llm | parser

# call chain
response = chain.invoke({"question": "function", "language": "Python"})
print(response)


