from langchain_community.document_loaders import (
    UnstructuredPDFLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredPowerPointLoader,
    UnstructuredExcelLoader,
    TextLoader,
)
from langchain_community.document_loaders import PyPDFLoader

def load_docs(paths):
    docs = []
    for path in paths:
        try:
            if path.endswith(".pdf"):
                # loader = UnstructuredPDFLoader(path)
                loader = PyPDFLoader(path)
                docs.extend(loader.load())
            elif path.endswith(".docx") or path.endswith(".doc"):
                loader = UnstructuredWordDocumentLoader(path)
                docs.extend(loader.load())
            elif path.endswith(".pptx") or path.endswith(".ppt"):
                loader = UnstructuredPowerPointLoader(path)
                docs.extend(loader.load())
            elif path.endswith(".xlsx") or path.endswith(".xls"):
                loader = UnstructuredExcelLoader(path)
                docs.extend(loader.load())
            else:
                loader = TextLoader(path, encoding="utf-8")
                docs.extend(loader.load())
        except Exception as e:
            print(f"[WARN] Skip {path} because {e}")
    # print(f"Loaded {len(docs)} documents.")
    # print(docs)
    # exit(1)
    return docs

import pandas as pd
from langchain.schema import Document


def load_excel_all_sheets(path):
    xls = pd.ExcelFile(path)
    docs = []
    for sheet in xls.sheet_names:
        df = pd.read_excel(path, sheet_name=sheet)
        # แปลงแต่ละ row เป็น Document
        for i, row in df.iterrows():
            docs.append(
                Document(
                    page_content=row.to_json(force_ascii=False),
                    metadata={"source": path, "sheet": sheet, "row": i},
                )
            )
    return docs
