# chain
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain.schema.runnable import RunnablePassthrough, <PERSON><PERSON><PERSON>Lambda

from doc_loader import load_docs
from langchain.text_splitter import RecursiveCharacterTextSplitter

# vectorstore
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import FAISS
from langchain_community.retrievers import BM25Retriever
from langchain.retrievers import EnsembleRetriever

from typing import List
from dotenv import load_dotenv
load_dotenv()

# ---------- 1) Load documents ----------
docs = load_docs(["./data_doc/Employee_Training.docx", "./data_doc/SOP_Manual.pdf"])

# ---------- 2) Split ----------
# splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=140)
splitter = RecursiveCharacterTextSplitter(chunk_size=180, chunk_overlap=40)
chunks = splitter.split_documents(docs)
if len(chunks) == 0:
    print(f"[ERROR] No chunks from file")
    exit(1)

# ---------- 3) Create vector store ----------
embeddings = OpenAIEmbeddings(model="text-embedding-3-large")




# build FAISS retriever
vectorstore = FAISS.from_documents(chunks, embeddings)
retrieverFAISS = vectorstore.as_retriever(search_type="mmr", search_kwargs={"k": 6, "fetch_k": 20, "lambda_mult": 0.3})

# build BM25 retriever
retrieverBM25 = BM25Retriever.from_documents(chunks)
retrieverBM25.k = 6




# combine (hybrid retriever)
# Reciprocal rank fusion RRF-fuse ด้วย EnsembleRetriever
hybrid = EnsembleRetriever(
    retrievers=[retrieverFAISS, retrieverBM25],
    weights=[0.7, 0.3], #weights: น้ำหนักของแต่ละ retriever ตามลำดับ
    c=60, #(default = 60); ค่าน้อย = ให้ความสำคัญอันดับต้นมากขึ้น
)
retriever = hybrid




# ----------  Context Builder ----------
def build_context_fn(docs: list):
    print('--------------------------------------------------------------')
    print(f"[Context Builder] docs: {docs}")
    print('--------------------------------------------------------------\n')

    context = "\n\n## ส่วนที่เกี่ยวข้อง\n"
    for i, d in enumerate(docs):
        # context += f"\n### Section {i} [#{i}]\n"
        if "page" in d.metadata:
            context += f"**Page:** {d.metadata['page']}\n\n"
        if "source" in d.metadata:
            context += f"**Source:** {d.metadata['source']}\n\n"
        context += f"{d.page_content}\n"

    print("\n\n----- context -----")
    return context

build_context = RunnableLambda(build_context_fn)



# prompt template
SYSTEM_PROMPT = ("คุณคือผู้ช่วยค้นหาข้อมูลจากเอกสาร (พร้อมอ้างอิงแหล่งที่มา)")
USER_PROMPT = ("""
คำถาม: {question}  
เอกสาร: {context}
"""
)

prompt = ChatPromptTemplate.from_messages([
    ("system", SYSTEM_PROMPT),
    ("user", USER_PROMPT),
])

# llm model
llm = ChatOpenAI(model="gpt-5-mini", temperature=0)

# chain
chain = retriever | prompt | llm | StrOutputParser()
chain = (
    {"context": retriever | build_context, "question": RunnablePassthrough()}
    | prompt
    | llm
    | StrOutputParser()
)


print('\n\n---------------------------------------------------------------------------------------\n')
result = chain.invoke("ทำไมการ “อบรมพนักงานใหม่” จึงสำคัญ")
print(result)
print('\n\n---------------------------------------------------------------------------------------\n')
result = chain.invoke("เราจะเริ่มต้นเขียน SOP อย่างไร")
print(result)
print('\n\n---------------------------------------------------------------------------------------\n')
result = chain.invoke("สรุปเอกสาร SOP_Manual และ Employee_Training ให้หน่อย")
print(result)